// 配置工具函数
// 统一管理应用配置，支持开发和生产环境

/**
 * 环境类型枚举
 */
export enum Environment {
  DEVELOPMENT = 'development',
  TESTING = 'testing',
  STAGING = 'staging',
  PRODUCTION = 'production'
}

/**
 * 默认环境配置
 */
const DEFAULT_URLS = {
  [Environment.DEVELOPMENT]: 'http://localhost:3000',
  [Environment.TESTING]: 'http://localhost:3001',
  [Environment.STAGING]: 'http://localhost:3456',
  [Environment.PRODUCTION]: 'http://localhost:3456'
}

/**
 * 获取当前环境类型
 * @returns 当前环境类型
 */
export function getCurrentEnvironment(): Environment {
  // 优先使用明确的环境变量
  const envType = process.env.PLASMO_PUBLIC_ENV_TYPE
  if (envType && Object.values(Environment).includes(envType as Environment)) {
    return envType as Environment
  }

  // 根据NODE_ENV判断
  const nodeEnv = process.env.NODE_ENV
  if (nodeEnv === 'production') {
    return Environment.PRODUCTION
  } else {
    return Environment.DEVELOPMENT
  }
}

/**
 * 获取下载器基础URL
 * @returns 下载器基础URL
 */
export function getDownloaderBaseUrl(): string {
  // 优先使用环境变量配置
  const envUrl = process.env.PLASMO_PUBLIC_DOWNLOADER_URL
  if (envUrl) {
    return envUrl
  }

  // 根据当前环境获取默认配置
  const currentEnv = getCurrentEnvironment()
  return DEFAULT_URLS[currentEnv]
}

/**
 * 获取完整的下载器URL
 * @param requestId 请求ID
 * @returns 完整的下载器URL
 */
export function getDownloaderUrl(requestId: string): string {
  const baseUrl = getDownloaderBaseUrl()
  return `${baseUrl}/downloader?requestId=${requestId}`
}

/**
 * 获取下载器页面的匹配模式（用于content script）
 * @returns 匹配模式数组
 */
export function getDownloaderMatches(): string[] {
  // 包含所有可能的下载器URL模式
  return [
    "http://localhost:3000/*",
    "http://localhost:3456/*"
  ]
}

/**
 * 检查当前是否为开发环境
 * @returns 是否为开发环境
 */
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development'
}

/**
 * 检查当前是否为生产环境
 * @returns 是否为生产环境
 */
export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production'
}
